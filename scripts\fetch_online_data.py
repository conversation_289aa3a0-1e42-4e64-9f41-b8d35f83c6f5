#!/usr/bin/env python3
"""
在线数据获取脚本
用于调用在线模式工具获取四个分析师代理所需的数据并保存到本地

使用方法:
python scripts/fetch_online_data.py --symbols AAPL,MSFT --start-date 2024-01-01 --end-date 2024-01-31
"""

import os
import sys
import json
import argparse
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入TradingAgents模块
from tradingagents.dataflows import interface
from tradingagents.default_config import DEFAULT_CONFIG
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class OnlineDataFetcher:
    """在线数据获取器"""
    
    def __init__(self, data_dir: str = None):
        """
        初始化数据获取器
        
        Args:
            data_dir: 数据保存目录，默认使用配置中的data_dir
        """
        self.data_dir = Path(data_dir) if data_dir else Path(DEFAULT_CONFIG["data_dir"])
        self.cache_dir = Path(DEFAULT_CONFIG["data_cache_dir"])
        
        # 确保目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查API密钥
        self._check_api_keys()
        
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📁 缓存目录: {self.cache_dir}")
    
    def _check_api_keys(self):
        """检查必需的API密钥"""
        required_keys = {
            'DEEPSEEK_API_KEY': '用于LLM分析',
            'FINNHUB_API_KEY': '用于金融数据'
        }
        
        missing_keys = []
        for key, desc in required_keys.items():
            if not os.getenv(key):
                missing_keys.append(f"{key} ({desc})")
        
        if missing_keys:
            print("⚠️  缺少以下API密钥:")
            for key in missing_keys:
                print(f"   - {key}")
            print("\n请在.env文件中配置这些密钥")
        else:
            print("✅ API密钥检查通过")
    
    def fetch_market_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取市场分析师数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            获取的数据字典
        """
        print("\n🏢 获取市场分析师数据...")
        market_data = {}
        
        for symbol in symbols:
            print(f"📊 处理股票: {symbol}")
            
            try:
                # 1. 获取Yahoo Finance价格数据
                print(f"   获取价格数据...")
                price_data = interface.get_YFin_data_online(symbol, start_date, end_date)
                
                # 保存价格数据
                price_file = self.data_dir / "market_data" / "price_data" / f"{symbol}-YFin-data-{start_date}-{end_date}.csv"
                price_file.parent.mkdir(parents=True, exist_ok=True)
                
                if isinstance(price_data, pd.DataFrame):
                    price_data.to_csv(price_file, index=False)
                    print(f"   ✅ 价格数据已保存: {price_file}")
                else:
                    # 如果返回的是字符串，保存为文本文件
                    with open(price_file.with_suffix('.txt'), 'w', encoding='utf-8') as f:
                        f.write(str(price_data))
                    print(f"   ✅ 价格数据已保存: {price_file.with_suffix('.txt')}")
                
                # 2. 获取技术指标数据
                print(f"   获取技术指标...")
                indicators = ['rsi_14', 'macd', 'boll', 'ma_20', 'ema_12']
                indicator_data = {}
                
                for indicator in indicators:
                    try:
                        indicator_result = interface.get_stock_stats_indicators_window(
                            symbol, indicator, end_date, 30, True  # online=True
                        )
                        indicator_data[indicator] = indicator_result
                        print(f"     ✅ {indicator}")
                        time.sleep(0.5)  # 避免请求过快
                    except Exception as e:
                        print(f"     ❌ {indicator}: {e}")
                        indicator_data[indicator] = f"Error: {e}"
                
                # 保存技术指标数据
                indicator_file = self.data_dir / "market_data" / "indicators" / f"{symbol}-indicators-{end_date}.json"
                indicator_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(indicator_file, 'w', encoding='utf-8') as f:
                    json.dump(indicator_data, f, ensure_ascii=False, indent=2)
                print(f"   ✅ 技术指标已保存: {indicator_file}")
                
                market_data[symbol] = {
                    'price_data': str(price_file),
                    'indicators': indicator_data
                }
                
            except Exception as e:
                print(f"   ❌ 获取 {symbol} 数据失败: {e}")
                market_data[symbol] = {'error': str(e)}
        
        return market_data
    
    def fetch_news_data(self, symbols: List[str], target_date: str) -> Dict[str, Any]:
        """
        获取新闻分析师数据
        
        Args:
            symbols: 股票代码列表
            target_date: 目标日期 (YYYY-MM-DD)
            
        Returns:
            获取的数据字典
        """
        print("\n📰 获取新闻分析师数据...")
        news_data = {}
        
        try:
            # 1. 获取全球新闻
            print("   获取全球新闻...")
            global_news = interface.get_global_news_openai(target_date)
            
            # 保存全球新闻
            global_news_file = self.data_dir / "news_data" / "global" / f"global-news-{target_date}.txt"
            global_news_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(global_news_file, 'w', encoding='utf-8') as f:
                f.write(global_news)
            print(f"   ✅ 全球新闻已保存: {global_news_file}")
            
            news_data['global_news'] = {
                'file': str(global_news_file),
                'content': global_news
            }
            
            # 2. 获取Google新闻
            for symbol in symbols:
                print(f"   获取 {symbol} Google新闻...")
                try:
                    google_news = interface.get_google_news(symbol, target_date, 7)
                    
                    # 保存Google新闻
                    google_news_file = self.data_dir / "news_data" / "google" / f"{symbol}-google-news-{target_date}.txt"
                    google_news_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    with open(google_news_file, 'w', encoding='utf-8') as f:
                        f.write(str(google_news))
                    print(f"     ✅ Google新闻已保存: {google_news_file}")
                    
                    news_data[f'{symbol}_google_news'] = {
                        'file': str(google_news_file),
                        'content': google_news
                    }
                    
                    time.sleep(1)  # 避免请求过快
                    
                except Exception as e:
                    print(f"     ❌ 获取 {symbol} Google新闻失败: {e}")
                    news_data[f'{symbol}_google_news'] = {'error': str(e)}
            
        except Exception as e:
            print(f"   ❌ 获取新闻数据失败: {e}")
            news_data['error'] = str(e)
        
        return news_data
    
    def fetch_social_media_data(self, symbols: List[str], target_date: str) -> Dict[str, Any]:
        """
        获取社交媒体分析师数据
        
        Args:
            symbols: 股票代码列表
            target_date: 目标日期 (YYYY-MM-DD)
            
        Returns:
            获取的数据字典
        """
        print("\n📱 获取社交媒体分析师数据...")
        social_data = {}
        
        for symbol in symbols:
            print(f"   获取 {symbol} 社交媒体数据...")
            try:
                # 获取股票相关社交媒体数据
                social_news = interface.get_stock_news_openai(symbol, target_date)
                
                # 保存社交媒体数据
                social_file = self.data_dir / "social_data" / f"{symbol}-social-{target_date}.txt"
                social_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(social_file, 'w', encoding='utf-8') as f:
                    f.write(social_news)
                print(f"     ✅ 社交媒体数据已保存: {social_file}")
                
                social_data[symbol] = {
                    'file': str(social_file),
                    'content': social_news
                }
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"     ❌ 获取 {symbol} 社交媒体数据失败: {e}")
                social_data[symbol] = {'error': str(e)}
        
        return social_data
    
    def fetch_fundamentals_data(self, symbols: List[str], target_date: str) -> Dict[str, Any]:
        """
        获取基本面分析师数据
        
        Args:
            symbols: 股票代码列表
            target_date: 目标日期 (YYYY-MM-DD)
            
        Returns:
            获取的数据字典
        """
        print("\n📈 获取基本面分析师数据...")
        fundamentals_data = {}
        
        for symbol in symbols:
            print(f"   获取 {symbol} 基本面数据...")
            try:
                # 获取基本面数据
                fundamentals = interface.get_fundamentals_openai(symbol, target_date)
                
                # 保存基本面数据
                fundamentals_file = self.data_dir / "fundamentals_data" / f"{symbol}-fundamentals-{target_date}.txt"
                fundamentals_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(fundamentals_file, 'w', encoding='utf-8') as f:
                    f.write(fundamentals)
                print(f"     ✅ 基本面数据已保存: {fundamentals_file}")
                
                fundamentals_data[symbol] = {
                    'file': str(fundamentals_file),
                    'content': fundamentals
                }
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"     ❌ 获取 {symbol} 基本面数据失败: {e}")
                fundamentals_data[symbol] = {'error': str(e)}
        
        return fundamentals_data
    
    def fetch_all_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取所有分析师数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            所有获取的数据
        """
        print(f"🚀 开始获取数据...")
        print(f"   股票代码: {', '.join(symbols)}")
        print(f"   时间范围: {start_date} ~ {end_date}")
        
        all_data = {
            'metadata': {
                'symbols': symbols,
                'start_date': start_date,
                'end_date': end_date,
                'fetch_time': datetime.now().isoformat()
            }
        }
        
        # 获取各类数据
        all_data['market_data'] = self.fetch_market_data(symbols, start_date, end_date)
        all_data['news_data'] = self.fetch_news_data(symbols, end_date)
        all_data['social_data'] = self.fetch_social_media_data(symbols, end_date)
        all_data['fundamentals_data'] = self.fetch_fundamentals_data(symbols, end_date)
        
        # 保存汇总信息
        summary_file = self.data_dir / f"data_summary_{end_date}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 数据获取完成!")
        print(f"📄 汇总文件: {summary_file}")
        
        return all_data


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='获取TradingAgents在线数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 获取单个股票数据
  python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31

  # 获取多个股票数据
  python scripts/fetch_online_data.py --symbols AAPL,MSFT,GOOGL --start-date 2024-01-01 --end-date 2024-01-31

  # 指定自定义数据目录
  python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --data-dir ./my_data

  # 只获取特定类型的数据
  python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts market,news

注意事项:
  - 需要配置 DEEPSEEK_API_KEY 和 FINNHUB_API_KEY 环境变量
  - 建议使用较短的时间范围以节省API调用成本
  - 数据将保存在 data/ 目录下的相应子目录中
        """
    )

    parser.add_argument('--symbols', required=True,
                       help='股票代码，用逗号分隔，如: AAPL,MSFT,GOOGL')
    parser.add_argument('--start-date', required=True,
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', required=True,
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--data-dir',
                       help='数据保存目录，默认使用配置中的目录')
    parser.add_argument('--analysts',
                       help='指定要获取的分析师数据，用逗号分隔: market,news,social,fundamentals (默认获取全部)')
    parser.add_argument('--delay', type=float, default=1.0,
                       help='API调用间隔时间(秒)，默认1秒')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出')

    args = parser.parse_args()

    # 解析股票代码
    symbols = [s.strip().upper() for s in args.symbols.split(',')]

    # 验证日期格式
    try:
        start_dt = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(args.end_date, '%Y-%m-%d')

        if start_dt > end_dt:
            print("❌ 开始日期不能晚于结束日期")
            return

        if end_dt > datetime.now():
            print("⚠️  结束日期晚于当前日期，某些数据可能无法获取")

    except ValueError:
        print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        return

    # 解析分析师类型
    if args.analysts:
        selected_analysts = [a.strip().lower() for a in args.analysts.split(',')]
        valid_analysts = ['market', 'news', 'social', 'fundamentals']
        invalid_analysts = [a for a in selected_analysts if a not in valid_analysts]
        if invalid_analysts:
            print(f"❌ 无效的分析师类型: {', '.join(invalid_analysts)}")
            print(f"   有效类型: {', '.join(valid_analysts)}")
            return
    else:
        selected_analysts = ['market', 'news', 'social', 'fundamentals']

    print("🔧 配置信息:")
    print(f"   股票代码: {', '.join(symbols)}")
    print(f"   时间范围: {args.start_date} ~ {args.end_date}")
    print(f"   分析师类型: {', '.join(selected_analysts)}")
    print(f"   API调用间隔: {args.delay}秒")

    try:
        # 创建数据获取器
        fetcher = OnlineDataFetcher(args.data_dir)
        fetcher.delay = args.delay
        fetcher.verbose = args.verbose

        # 执行数据获取
        all_data = {}
        all_data['metadata'] = {
            'symbols': symbols,
            'start_date': args.start_date,
            'end_date': args.end_date,
            'selected_analysts': selected_analysts,
            'fetch_time': datetime.now().isoformat()
        }

        # 根据选择获取数据
        if 'market' in selected_analysts:
            all_data['market_data'] = fetcher.fetch_market_data(symbols, args.start_date, args.end_date)

        if 'news' in selected_analysts:
            all_data['news_data'] = fetcher.fetch_news_data(symbols, args.end_date)

        if 'social' in selected_analysts:
            all_data['social_data'] = fetcher.fetch_social_media_data(symbols, args.end_date)

        if 'fundamentals' in selected_analysts:
            all_data['fundamentals_data'] = fetcher.fetch_fundamentals_data(symbols, args.end_date)

        # 保存汇总信息
        summary_file = fetcher.data_dir / f"data_summary_{args.end_date}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)

        print(f"\n🎉 数据获取完成!")
        print(f"📄 汇总文件: {summary_file}")
        print(f"📁 数据目录: {fetcher.data_dir}")

        # 显示统计信息
        total_files = sum(len(list(Path(fetcher.data_dir).rglob('*'))) for _ in [None])
        print(f"📊 共生成 {total_files} 个文件")

    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()


if __name__ == '__main__':
    main()
